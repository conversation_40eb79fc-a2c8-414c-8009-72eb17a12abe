import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/restaurant.dart';
import '../models/category.dart';
import '../services/data_service.dart';
import '../providers/cart_provider.dart';
import '../widgets/search_bar_widget.dart';
import '../widgets/category_list_widget.dart';
import '../widgets/restaurant_card_widget.dart';
import '../widgets/cart_fab_widget.dart';
import 'restaurant_detail_screen.dart';
import 'cart_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final DataService _dataService = DataService();
  final TextEditingController _searchController = TextEditingController();

  List<Restaurant> _restaurants = [];
  List<Restaurant> _filteredRestaurants = [];
  List<FoodCategory> _categories = [];
  bool _isLoading = true;
  String _errorMessage = '';
  String _selectedCategory = '';

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      final restaurants = await _dataService.getRestaurants();
      final categories = await _dataService.getCategories();

      setState(() {
        _restaurants = restaurants;
        _filteredRestaurants = restaurants;
        _categories = categories;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to load data: $e';
        _isLoading = false;
      });
    }
  }

  void _onSearchChanged(String query) {
    setState(() {
      if (query.isEmpty && _selectedCategory.isEmpty) {
        _filteredRestaurants = _restaurants;
      } else {
        _filteredRestaurants = _restaurants.where((restaurant) {
          final matchesSearch =
              query.isEmpty ||
              restaurant.name.toLowerCase().contains(query.toLowerCase()) ||
              restaurant.cuisine.toLowerCase().contains(query.toLowerCase()) ||
              restaurant.description.toLowerCase().contains(
                query.toLowerCase(),
              );

          final matchesCategory =
              _selectedCategory.isEmpty ||
              restaurant.categories.any(
                (category) =>
                    category.toLowerCase() == _selectedCategory.toLowerCase(),
              );

          return matchesSearch && matchesCategory;
        }).toList();
      }
    });
  }

  void _onCategorySelected(String categoryName) {
    setState(() {
      _selectedCategory = _selectedCategory == categoryName ? '' : categoryName;
    });
    _onSearchChanged(_searchController.text);
  }

  void _onRestaurantTapped(Restaurant restaurant) {
    Navigator.push(
      context,
      MaterialPageRoute<void>(
        builder: (context) => RestaurantDetailScreen(restaurant: restaurant),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Deliver to',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
            Row(
              children: [
                const Icon(Icons.location_on, size: 16, color: Colors.green),
                const SizedBox(width: 4),
                const Text(
                  'Current Location',
                  style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                ),
                const SizedBox(width: 4),
                Icon(
                  Icons.keyboard_arrow_down,
                  size: 16,
                  color: Colors.grey[600],
                ),
              ],
            ),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.person_outline),
            onPressed: () {
              // TODO: Navigate to profile
            },
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage.isNotEmpty
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error_outline, size: 64, color: Colors.red),
                  const SizedBox(height: 16),
                  Text(_errorMessage, textAlign: TextAlign.center),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: _loadData,
                    child: const Text('Retry'),
                  ),
                ],
              ),
            )
          : RefreshIndicator(
              onRefresh: _loadData,
              child: CustomScrollView(
                slivers: [
                  // Search Bar
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: SearchBarWidget(
                        controller: _searchController,
                        onChanged: _onSearchChanged,
                      ),
                    ),
                  ),

                  // Categories
                  SliverToBoxAdapter(
                    child: CategoryListWidget(
                      categories: _categories,
                      selectedCategory: _selectedCategory,
                      onCategorySelected: _onCategorySelected,
                    ),
                  ),

                  // Section Header
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Text(
                        _selectedCategory.isEmpty
                            ? 'All Restaurants (${_filteredRestaurants.length})'
                            : '$_selectedCategory Restaurants (${_filteredRestaurants.length})',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),

                  // Restaurants List
                  _filteredRestaurants.isEmpty
                      ? const SliverToBoxAdapter(
                          child: Center(
                            child: Padding(
                              padding: EdgeInsets.all(32.0),
                              child: Text(
                                'No restaurants found',
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Colors.grey,
                                ),
                              ),
                            ),
                          ),
                        )
                      : SliverList(
                          delegate: SliverChildBuilderDelegate((
                            context,
                            index,
                          ) {
                            final restaurant = _filteredRestaurants[index];
                            return Padding(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 16.0,
                                vertical: 8.0,
                              ),
                              child: RestaurantCardWidget(
                                restaurant: restaurant,
                                onTap: () => _onRestaurantTapped(restaurant),
                              ),
                            );
                          }, childCount: _filteredRestaurants.length),
                        ),
                ],
              ),
            ),
      floatingActionButton: Consumer<CartProvider>(
        builder: (context, cartProvider, child) {
          return cartProvider.isNotEmpty
              ? CartFabWidget(
                  itemCount: cartProvider.itemCount,
                  total: cartProvider.total,
                  onPressed: () {
                    // TODO: Navigate to cart screen
                  },
                )
              : const SizedBox.shrink();
        },
      ),
    );
  }
}

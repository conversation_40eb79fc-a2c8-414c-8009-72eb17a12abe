import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/search_provider.dart';
import '../widgets/search_bar_widget.dart';
import '../widgets/restaurant_card_widget.dart';
import '../widgets/filter_bottom_sheet.dart';
import 'restaurant_detail_screen.dart';
import '../utils/animations.dart';
import '../utils/navigation_helper.dart';

class SearchScreen extends StatefulWidget {
  const SearchScreen({super.key});

  @override
  State<SearchScreen> createState() => _SearchScreenState();
}

class _SearchScreenState extends State<SearchScreen> {
  final TextEditingController _searchController = TextEditingController();
  late SearchProvider _searchProvider;

  @override
  void initState() {
    super.initState();
    _searchProvider = Provider.of<SearchProvider>(context, listen: false);
    _searchProvider.loadData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _showFilterBottomSheet() {
    showModalBottomSheet<void>(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => FilterBottomSheet(searchProvider: _searchProvider),
    );
  }

  void _showSortOptions() {
    showModalBottomSheet<void>(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Sort by',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ...SortOption.values.map((option) {
              return ListTile(
                title: Text(_getSortOptionName(option)),
                onTap: () {
                  _searchProvider.sortRestaurants(option);
                  Navigator.pop(context);
                },
              );
            }),
          ],
        ),
      ),
    );
  }

  String _getSortOptionName(SortOption option) {
    switch (option) {
      case SortOption.rating:
        return 'Rating (High to Low)';
      case SortOption.deliveryTime:
        return 'Delivery Time (Fast to Slow)';
      case SortOption.deliveryFee:
        return 'Delivery Fee (Low to High)';
      case SortOption.popularity:
        return 'Popularity';
      case SortOption.alphabetical:
        return 'Alphabetical';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Search Restaurants'),
        actions: [
          IconButton(
            icon: const Icon(Icons.tune),
            onPressed: _showFilterBottomSheet,
          ),
          IconButton(icon: const Icon(Icons.sort), onPressed: _showSortOptions),
        ],
      ),
      body: Consumer<SearchProvider>(
        builder: (context, searchProvider, child) {
          return Column(
            children: [
              // Search Bar
              Padding(
                padding: const EdgeInsets.all(16),
                child: SearchBarWidget(
                  controller: _searchController,
                  onChanged: searchProvider.updateSearchQuery,
                  hintText: 'Search restaurants, cuisines, or dishes...',
                ),
              ),

              // Active Filters
              if (searchProvider.selectedCategory.isNotEmpty ||
                  searchProvider.minRating > 0 ||
                  searchProvider.maxDeliveryTime < 60)
                Container(
                  height: 50,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: ListView(
                    scrollDirection: Axis.horizontal,
                    children: [
                      if (searchProvider.selectedCategory.isNotEmpty)
                        _buildFilterChip(
                          searchProvider.selectedCategory,
                          () => searchProvider.updateSelectedCategory(''),
                        ),
                      if (searchProvider.minRating > 0)
                        _buildFilterChip(
                          '${searchProvider.minRating}+ stars',
                          () => searchProvider.updateMinRating(0),
                        ),
                      if (searchProvider.maxDeliveryTime < 60)
                        _buildFilterChip(
                          'Under ${searchProvider.maxDeliveryTime} min',
                          () => searchProvider.updateMaxDeliveryTime(60),
                        ),
                      if (searchProvider.selectedCategory.isNotEmpty ||
                          searchProvider.minRating > 0 ||
                          searchProvider.maxDeliveryTime < 60)
                        Padding(
                          padding: const EdgeInsets.only(left: 8),
                          child: TextButton(
                            onPressed: searchProvider.clearFilters,
                            child: const Text('Clear All'),
                          ),
                        ),
                    ],
                  ),
                ),

              // Results Count
              Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                child: Row(
                  children: [
                    Text(
                      '${searchProvider.filteredRestaurants.length} restaurants found',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),

              // Results
              Expanded(
                child: searchProvider.isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : searchProvider.errorMessage.isNotEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(
                              Icons.error_outline,
                              size: 64,
                              color: Colors.red,
                            ),
                            const SizedBox(height: 16),
                            Text(searchProvider.errorMessage),
                            const SizedBox(height: 16),
                            ElevatedButton(
                              onPressed: searchProvider.loadData,
                              child: const Text('Retry'),
                            ),
                          ],
                        ),
                      )
                    : searchProvider.filteredRestaurants.isEmpty
                    ? const Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.search_off,
                              size: 64,
                              color: Colors.grey,
                            ),
                            SizedBox(height: 16),
                            Text(
                              'No restaurants found',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.grey,
                              ),
                            ),
                            SizedBox(height: 8),
                            Text(
                              'Try adjusting your search or filters',
                              style: TextStyle(color: Colors.grey),
                            ),
                          ],
                        ),
                      )
                    : ListView.builder(
                        padding: const EdgeInsets.all(16),
                        itemCount: searchProvider.filteredRestaurants.length,
                        itemBuilder: (context, index) {
                          final restaurant =
                              searchProvider.filteredRestaurants[index];
                          return FadeInUp(
                            delay: Duration(milliseconds: index * 50),
                            child: Padding(
                              padding: const EdgeInsets.only(bottom: 16),
                              child: RestaurantCardWidget(
                                restaurant: restaurant,
                                onTap: () {
                                  NavigationHelper.slideToPage<void>(
                                    context,
                                    RestaurantDetailScreen(
                                      restaurant: restaurant,
                                    ),
                                    direction: SlideDirection.right,
                                  );
                                },
                              ),
                            ),
                          );
                        },
                      ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildFilterChip(String label, VoidCallback onRemove) {
    return Padding(
      padding: const EdgeInsets.only(right: 8),
      child: Chip(
        label: Text(label),
        deleteIcon: const Icon(Icons.close, size: 18),
        onDeleted: onRemove,
        backgroundColor: const Color(0xFF00D4AA).withValues(alpha: 0.1),
        deleteIconColor: const Color(0xFF00D4AA),
        labelStyle: const TextStyle(color: Color(0xFF00D4AA)),
      ),
    );
  }
}

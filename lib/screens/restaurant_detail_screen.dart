import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import '../models/restaurant.dart';
import '../providers/cart_provider.dart';
import '../widgets/food_item_widget.dart';

class RestaurantDetailScreen extends StatefulWidget {
  final Restaurant restaurant;

  const RestaurantDetailScreen({
    super.key,
    required this.restaurant,
  });

  @override
  State<RestaurantDetailScreen> createState() => _RestaurantDetailScreenState();
}

class _RestaurantDetailScreenState extends State<RestaurantDetailScreen> {
  final ScrollController _scrollController = ScrollController();
  String _selectedCategory = '';
  
  @override
  void initState() {
    super.initState();
    // Set first category as default
    if (widget.restaurant.menu.isNotEmpty) {
      _selectedCategory = widget.restaurant.menu.first.category;
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  List<String> get _menuCategories {
    final categories = widget.restaurant.menu
        .map((item) => item.category)
        .toSet()
        .toList();
    return categories;
  }

  List<FoodItem> get _filteredMenuItems {
    if (_selectedCategory.isEmpty) return widget.restaurant.menu;
    return widget.restaurant.menu
        .where((item) => item.category == _selectedCategory)
        .toList();
  }

  void _showRestaurantChangeDialog(FoodItem foodItem) {
    final cartProvider = Provider.of<CartProvider>(context, listen: false);
    
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Replace cart items?'),
          content: Text(
            'Your cart contains items from ${cartProvider.restaurantName}. '
            'Do you want to replace them with items from ${widget.restaurant.name}?',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                cartProvider.replaceCartWithNewRestaurant(
                  foodItem: foodItem,
                  restaurantId: widget.restaurant.id,
                  restaurantName: widget.restaurant.name,
                );
                _showAddedToCartSnackBar(foodItem);
              },
              child: const Text('Replace'),
            ),
          ],
        );
      },
    );
  }

  void _addToCart(FoodItem foodItem) {
    final cartProvider = Provider.of<CartProvider>(context, listen: false);
    
    if (cartProvider.shouldShowRestaurantChangeDialog(widget.restaurant.id)) {
      _showRestaurantChangeDialog(foodItem);
      return;
    }
    
    cartProvider.addItem(
      foodItem: foodItem,
      restaurantId: widget.restaurant.id,
      restaurantName: widget.restaurant.name,
    );
    
    _showAddedToCartSnackBar(foodItem);
  }

  void _showAddedToCartSnackBar(FoodItem foodItem) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${foodItem.name} added to cart'),
        duration: const Duration(seconds: 2),
        action: SnackBarAction(
          label: 'View Cart',
          onPressed: () {
            // TODO: Navigate to cart screen
          },
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CustomScrollView(
        controller: _scrollController,
        slivers: [
          // App Bar with Restaurant Image
          SliverAppBar(
            expandedHeight: 250,
            pinned: true,
            flexibleSpace: FlexibleSpaceBar(
              background: CachedNetworkImage(
                imageUrl: widget.restaurant.coverImageUrl,
                fit: BoxFit.cover,
                placeholder: (context, url) => Container(
                  color: Colors.grey[300],
                  child: const Center(child: CircularProgressIndicator()),
                ),
                errorWidget: (context, url, error) => Container(
                  color: Colors.grey[300],
                  child: const Center(
                    child: Icon(Icons.restaurant, size: 60, color: Colors.grey),
                  ),
                ),
              ),
            ),
          ),
          
          // Restaurant Info
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.restaurant.name,
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    widget.restaurant.description,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      RatingBarIndicator(
                        rating: widget.restaurant.rating,
                        itemBuilder: (context, index) => const Icon(
                          Icons.star,
                          color: Colors.amber,
                        ),
                        itemCount: 5,
                        itemSize: 20.0,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        '${widget.restaurant.rating} (${widget.restaurant.reviewCount} reviews)',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      _buildInfoChip(
                        Icons.access_time,
                        '${widget.restaurant.deliveryTime} min',
                      ),
                      const SizedBox(width: 12),
                      _buildInfoChip(
                        Icons.delivery_dining,
                        '\$${widget.restaurant.deliveryFee.toStringAsFixed(2)}',
                      ),
                      const SizedBox(width: 12),
                      _buildInfoChip(
                        Icons.shopping_bag,
                        'Min \$${widget.restaurant.minimumOrder.toStringAsFixed(2)}',
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          
          // Category Tabs
          SliverToBoxAdapter(
            child: Container(
              height: 50,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: _menuCategories.length,
                itemBuilder: (context, index) {
                  final category = _menuCategories[index];
                  final isSelected = category == _selectedCategory;
                  
                  return Padding(
                    padding: const EdgeInsets.only(right: 12),
                    child: FilterChip(
                      label: Text(category),
                      selected: isSelected,
                      onSelected: (selected) {
                        setState(() {
                          _selectedCategory = category;
                        });
                      },
                      selectedColor: const Color(0xFF00D4AA),
                      checkmarkColor: Colors.white,
                      labelStyle: TextStyle(
                        color: isSelected ? Colors.white : Colors.black87,
                        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
          
          // Menu Items
          SliverList(
            delegate: SliverChildBuilderDelegate(
              (context, index) {
                final foodItem = _filteredMenuItems[index];
                return Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: FoodItemWidget(
                    foodItem: foodItem,
                    onAddToCart: () => _addToCart(foodItem),
                  ),
                );
              },
              childCount: _filteredMenuItems.length,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoChip(IconData icon, String text) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: Colors.grey[600]),
          const SizedBox(width: 4),
          Text(
            text,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}

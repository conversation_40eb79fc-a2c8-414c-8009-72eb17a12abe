import 'dart:convert';
import 'dart:math';
import 'package:flutter/services.dart';
import '../models/restaurant.dart';
import '../models/category.dart';

class DataService {
  static final DataService _instance = DataService._internal();
  factory DataService() => _instance;
  DataService._internal();

  List<Restaurant>? _restaurants;
  List<FoodCategory>? _categories;

  // Simulate network delay
  Future<void> _simulateNetworkDelay() async {
    final random = Random();
    final delay = 500 + random.nextInt(1000); // 500-1500ms delay
    await Future.delayed(Duration(milliseconds: delay));
  }

  Future<List<Restaurant>> getRestaurants() async {
    if (_restaurants != null) {
      await _simulateNetworkDelay();
      return _restaurants!;
    }

    try {
      await _simulateNetworkDelay();
      final String jsonString = await rootBundle.loadString(
        'assets/data/restaurants.json',
      );
      final List<dynamic> jsonData = json.decode(jsonString) as List<dynamic>;

      _restaurants = jsonData
          .map((json) => Restaurant.fromJson(json as Map<String, dynamic>))
          .toList();

      return _restaurants!;
    } catch (e) {
      throw Exception('Failed to load restaurants: $e');
    }
  }

  Future<List<FoodCategory>> getCategories() async {
    if (_categories != null) {
      await _simulateNetworkDelay();
      return _categories!;
    }

    try {
      await _simulateNetworkDelay();
      final String jsonString = await rootBundle.loadString(
        'assets/data/categories.json',
      );
      final List<dynamic> jsonData = json.decode(jsonString) as List<dynamic>;

      _categories = jsonData
          .map((json) => FoodCategory.fromJson(json as Map<String, dynamic>))
          .toList();

      return _categories!;
    } catch (e) {
      throw Exception('Failed to load categories: $e');
    }
  }

  Future<Restaurant?> getRestaurantById(String id) async {
    final restaurants = await getRestaurants();
    try {
      return restaurants.firstWhere((restaurant) => restaurant.id == id);
    } catch (e) {
      return null;
    }
  }

  Future<List<Restaurant>> searchRestaurants(String query) async {
    if (query.isEmpty) return await getRestaurants();

    final restaurants = await getRestaurants();
    final lowercaseQuery = query.toLowerCase();

    return restaurants.where((restaurant) {
      return restaurant.name.toLowerCase().contains(lowercaseQuery) ||
          restaurant.cuisine.toLowerCase().contains(lowercaseQuery) ||
          restaurant.description.toLowerCase().contains(lowercaseQuery) ||
          restaurant.categories.any(
            (category) => category.toLowerCase().contains(lowercaseQuery),
          );
    }).toList();
  }

  Future<List<Restaurant>> getRestaurantsByCategory(String categoryName) async {
    final restaurants = await getRestaurants();

    return restaurants.where((restaurant) {
      return restaurant.categories.any(
        (category) => category.toLowerCase() == categoryName.toLowerCase(),
      );
    }).toList();
  }

  Future<List<Restaurant>> getFeaturedRestaurants() async {
    final restaurants = await getRestaurants();

    // Return restaurants with rating >= 4.5 or sort by rating
    final featured = restaurants
        .where((restaurant) => restaurant.rating >= 4.5)
        .toList();
    featured.sort((a, b) => b.rating.compareTo(a.rating));

    return featured.take(5).toList();
  }

  Future<List<Restaurant>> getPopularRestaurants() async {
    final restaurants = await getRestaurants();

    // Sort by review count (popularity)
    final popular = List<Restaurant>.from(restaurants);
    popular.sort((a, b) => b.reviewCount.compareTo(a.reviewCount));

    return popular.take(10).toList();
  }

  Future<List<Restaurant>> getNearbyRestaurants({
    double? userLatitude,
    double? userLongitude,
    double radiusKm = 10.0,
  }) async {
    final restaurants = await getRestaurants();

    if (userLatitude == null || userLongitude == null) {
      // If no user location, return all restaurants
      return restaurants;
    }

    // Simple distance calculation (not accurate for production)
    return restaurants.where((restaurant) {
      final distance = _calculateDistance(
        userLatitude,
        userLongitude,
        restaurant.address.latitude,
        restaurant.address.longitude,
      );
      return distance <= radiusKm;
    }).toList();
  }

  Future<List<FoodItem>> searchFoodItems(String query) async {
    if (query.isEmpty) return [];

    final restaurants = await getRestaurants();
    final lowercaseQuery = query.toLowerCase();
    final List<FoodItem> allFoodItems = [];

    for (final restaurant in restaurants) {
      allFoodItems.addAll(restaurant.menu);
    }

    return allFoodItems.where((foodItem) {
      return foodItem.name.toLowerCase().contains(lowercaseQuery) ||
          foodItem.description.toLowerCase().contains(lowercaseQuery) ||
          foodItem.category.toLowerCase().contains(lowercaseQuery);
    }).toList();
  }

  // Simple distance calculation using Haversine formula (simplified)
  double _calculateDistance(
    double lat1,
    double lon1,
    double lat2,
    double lon2,
  ) {
    const double earthRadius = 6371; // Earth's radius in kilometers

    final double dLat = _degreesToRadians(lat2 - lat1);
    final double dLon = _degreesToRadians(lon2 - lon1);

    final double a =
        sin(dLat / 2) * sin(dLat / 2) +
        cos(_degreesToRadians(lat1)) *
            cos(_degreesToRadians(lat2)) *
            sin(dLon / 2) *
            sin(dLon / 2);

    final double c = 2 * atan2(sqrt(a), sqrt(1 - a));

    return earthRadius * c;
  }

  double _degreesToRadians(double degrees) {
    return degrees * (pi / 180);
  }

  // Clear cache (useful for testing or refreshing data)
  void clearCache() {
    _restaurants = null;
    _categories = null;
  }
}

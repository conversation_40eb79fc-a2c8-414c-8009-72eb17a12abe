import 'package:flutter/material.dart';
import '../models/restaurant.dart';
import '../widgets/restaurant_card_widget.dart';
import '../utils/animations.dart';

class SwipeableRestaurantCard extends StatefulWidget {
  final Restaurant restaurant;
  final VoidCallback onTap;
  final VoidCallback? onFavorite;
  final VoidCallback? onShare;
  final bool isFavorite;

  const SwipeableRestaurantCard({
    super.key,
    required this.restaurant,
    required this.onTap,
    this.onFavorite,
    this.onShare,
    this.isFavorite = false,
  });

  @override
  State<SwipeableRestaurantCard> createState() =>
      _SwipeableRestaurantCardState();
}

class _SwipeableRestaurantCardState extends State<SwipeableRestaurantCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: AppAnimations.medium,
      vsync: this,
    );

    _slideAnimation =
        Tween<Offset>(begin: Offset.zero, end: const Offset(-0.3, 0)).animate(
          CurvedAnimation(
            parent: _animationController,
            curve: AppAnimations.easeInOut,
          ),
        );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: AppAnimations.easeInOut,
      ),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onPanUpdate(DragUpdateDetails details) {
    if (details.delta.dx < 0) {
      // Swiping left - show actions
      final progress = (-details.delta.dx / 100).clamp(0.0, 1.0);
      _animationController.value = (_animationController.value + progress)
          .clamp(0.0, 1.0);
    } else if (details.delta.dx > 0) {
      // Swiping right - also show actions (or reset if already showing)
      if (_animationController.value > 0) {
        // If actions are showing, reset them
        final progress = (details.delta.dx / 100).clamp(0.0, 1.0);
        _animationController.value = (_animationController.value - progress)
            .clamp(0.0, 1.0);
      } else {
        // If actions are not showing, show them
        final progress = (details.delta.dx / 100).clamp(0.0, 1.0);
        _animationController.value = (_animationController.value + progress)
            .clamp(0.0, 1.0);
      }
    }
  }

  void _onPanEnd(DragEndDetails details) {
    if (_animationController.value > 0.5) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }
  }

  void _resetSwipe() {
    _animationController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onPanUpdate: _onPanUpdate,
      onPanEnd: _onPanEnd,
      onTap: _resetSwipe,
      child: Stack(
        children: [
          // Background actions (revealed when swiped)
          Positioned.fill(
            child: Container(
              margin: const EdgeInsets.symmetric(vertical: 8),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                gradient: LinearGradient(
                  colors: [
                    Colors.orange.withValues(alpha: 0.8),
                    Colors.red.withValues(alpha: 0.8),
                  ],
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  if (widget.onFavorite != null)
                    Padding(
                      padding: const EdgeInsets.only(right: 20),
                      child: GestureDetector(
                        onTap: () {
                          widget.onFavorite?.call();
                          _resetSwipe();
                        },
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              widget.isFavorite
                                  ? Icons.favorite
                                  : Icons.favorite_border,
                              color: Colors.white,
                              size: 28,
                            ),
                            const SizedBox(height: 4),
                            Text(
                              widget.isFavorite ? 'Unfavorite' : 'Favorite',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  if (widget.onShare != null)
                    Padding(
                      padding: const EdgeInsets.only(right: 20),
                      child: GestureDetector(
                        onTap: () {
                          widget.onShare?.call();
                          _resetSwipe();
                        },
                        child: const Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.share, color: Colors.white, size: 28),
                            SizedBox(height: 4),
                            Text(
                              'Share',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),

          // Main restaurant card
          AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              return Transform.translate(
                offset: Offset(
                  _slideAnimation.value.dx * MediaQuery.of(context).size.width,
                  0,
                ),
                child: Transform.scale(
                  scale: _scaleAnimation.value,
                  child: RestaurantCardWidget(
                    restaurant: widget.restaurant,
                    onTap: widget.onTap,
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}

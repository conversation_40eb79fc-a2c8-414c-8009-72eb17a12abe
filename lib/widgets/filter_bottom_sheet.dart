import 'package:flutter/material.dart';
import '../providers/search_provider.dart';

class FilterBottomSheet extends StatefulWidget {
  final SearchProvider searchProvider;

  const FilterBottomSheet({super.key, required this.searchProvider});

  @override
  State<FilterBottomSheet> createState() => _FilterBottomSheetState();
}

class _FilterBottomSheetState extends State<FilterBottomSheet> {
  late double _tempMinRating;
  late int _tempMaxDeliveryTime;
  late String _tempSelectedCategory;

  @override
  void initState() {
    super.initState();
    _tempMinRating = widget.searchProvider.minRating;
    _tempMaxDeliveryTime = widget.searchProvider.maxDeliveryTime;
    _tempSelectedCategory = widget.searchProvider.selectedCategory;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Filters',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              TextButton(
                onPressed: () {
                  setState(() {
                    _tempMinRating = 0.0;
                    _tempMaxDeliveryTime = 60;
                    _tempSelectedCategory = '';
                  });
                },
                child: const Text('Clear All'),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // Categories
          const Text(
            'Categories',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: widget.searchProvider.categories.map((category) {
              final isSelected = _tempSelectedCategory == category.name;
              return FilterChip(
                label: Text(category.name),
                selected: isSelected,
                onSelected: (selected) {
                  setState(() {
                    _tempSelectedCategory = selected ? category.name : '';
                  });
                },
                selectedColor: const Color(0xFF00D4AA),
                checkmarkColor: Colors.white,
                labelStyle: TextStyle(
                  color: isSelected ? Colors.white : Colors.black87,
                ),
              );
            }).toList(),
          ),

          const SizedBox(height: 24),

          // Rating Filter
          const Text(
            'Minimum Rating',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: Slider(
                  value: _tempMinRating,
                  min: 0.0,
                  max: 5.0,
                  divisions: 10,
                  activeColor: const Color(0xFF00D4AA),
                  label: _tempMinRating == 0.0
                      ? 'Any'
                      : '${_tempMinRating.toStringAsFixed(1)}+',
                  onChanged: (value) {
                    setState(() {
                      _tempMinRating = value;
                    });
                  },
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  _tempMinRating == 0.0
                      ? 'Any'
                      : '${_tempMinRating.toStringAsFixed(1)}+',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
            ],
          ),

          const SizedBox(height: 24),

          // Delivery Time Filter
          const Text(
            'Maximum Delivery Time',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: Slider(
                  value: _tempMaxDeliveryTime.toDouble(),
                  min: 10.0,
                  max: 60.0,
                  divisions: 10,
                  activeColor: const Color(0xFF00D4AA),
                  label: '$_tempMaxDeliveryTime min',
                  onChanged: (value) {
                    setState(() {
                      _tempMaxDeliveryTime = value.round();
                    });
                  },
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  '$_tempMaxDeliveryTime min',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
            ],
          ),

          const SizedBox(height: 32),

          // Apply Button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () {
                widget.searchProvider.updateSelectedCategory(
                  _tempSelectedCategory,
                );
                widget.searchProvider.updateMinRating(_tempMinRating);
                widget.searchProvider.updateMaxDeliveryTime(
                  _tempMaxDeliveryTime,
                );
                Navigator.pop(context);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF00D4AA),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: const Text(
                'Apply Filters',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
            ),
          ),

          // Add bottom padding for safe area
          SizedBox(height: MediaQuery.of(context).padding.bottom),
        ],
      ),
    );
  }
}

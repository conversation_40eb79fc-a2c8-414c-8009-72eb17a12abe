import 'package:flutter/material.dart';
import '../utils/animations.dart';

class SwipeToDeleteWrapper extends StatelessWidget {
  final Widget child;
  final String itemId;
  final String itemName;
  final VoidCallback onDelete;
  final Color backgroundColor;
  final IconData deleteIcon;
  final String deleteText;
  final bool showConfirmDialog;

  const SwipeToDeleteWrapper({
    super.key,
    required this.child,
    required this.itemId,
    required this.itemName,
    required this.onDelete,
    this.backgroundColor = Colors.red,
    this.deleteIcon = Icons.delete,
    this.deleteText = 'Delete',
    this.showConfirmDialog = true,
  });

  Future<bool?> _showDeleteConfirmation(BuildContext context) async {
    if (!showConfirmDialog) return true;

    return await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Icon(Icons.warning_amber_rounded, color: Colors.orange, size: 28),
              const SizedBox(width: 12),
              const Text('Confirm Delete'),
            ],
          ),
          content: Text(
            'Are you sure you want to delete "$itemName"?',
            style: const TextStyle(fontSize: 16),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text(
                'Cancel',
                style: TextStyle(
                  color: Colors.grey,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text(
                'Delete',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildDeleteBackground({
    required Alignment alignment,
    required EdgeInsets padding,
  }) {
    return Container(
      alignment: alignment,
      padding: padding,
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12),
        gradient: LinearGradient(
          colors: [backgroundColor, backgroundColor.withValues(alpha: 0.8)],
          begin: alignment == Alignment.centerLeft
              ? Alignment.centerLeft
              : Alignment.centerRight,
          end: alignment == Alignment.centerLeft
              ? Alignment.centerRight
              : Alignment.centerLeft,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          TweenAnimationBuilder<double>(
            duration: AppAnimations.fast,
            tween: Tween(begin: 1.0, end: 1.2),
            builder: (context, scale, child) {
              return Transform.scale(
                scale: scale,
                child: Icon(deleteIcon, color: Colors.white, size: 32),
              );
            },
          ),
          const SizedBox(height: 4),
          Text(
            deleteText,
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Dismissible(
      key: Key(itemId),
      direction: DismissDirection.horizontal,
      confirmDismiss: (direction) => _showDeleteConfirmation(context),
      onDismissed: (direction) {
        onDelete();

        // Show feedback snackbar
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(Icons.check_circle, color: Colors.white, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    '"$itemName" deleted successfully',
                    style: const TextStyle(fontWeight: FontWeight.w500),
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            duration: const Duration(seconds: 2),
            action: SnackBarAction(
              label: 'UNDO',
              textColor: Colors.white,
              onPressed: () {
                // In a real app, this would restore the item
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      'Undo functionality would restore "$itemName"',
                    ),
                    duration: const Duration(seconds: 1),
                  ),
                );
              },
            ),
          ),
        );
      },
      background: _buildDeleteBackground(
        alignment: Alignment.centerLeft,
        padding: const EdgeInsets.only(left: 20),
      ),
      secondaryBackground: _buildDeleteBackground(
        alignment: Alignment.centerRight,
        padding: const EdgeInsets.only(right: 20),
      ),
      child: child,
    );
  }
}

// Extension to make it easier to wrap widgets
extension SwipeToDeleteExtension on Widget {
  Widget swipeToDelete({
    required String itemId,
    required String itemName,
    required VoidCallback onDelete,
    Color backgroundColor = Colors.red,
    IconData deleteIcon = Icons.delete,
    String deleteText = 'Delete',
    bool showConfirmDialog = true,
  }) {
    return SwipeToDeleteWrapper(
      itemId: itemId,
      itemName: itemName,
      onDelete: onDelete,
      backgroundColor: backgroundColor,
      deleteIcon: deleteIcon,
      deleteText: deleteText,
      showConfirmDialog: showConfirmDialog,
      child: this,
    );
  }
}

import 'package:flutter/material.dart';
import '../utils/animations.dart';

class CartFabWidget extends StatefulWidget {
  final int itemCount;
  final double total;
  final VoidCallback onPressed;

  const CartFabWidget({
    super.key,
    required this.itemCount,
    required this.total,
    required this.onPressed,
  });

  @override
  State<CartFabWidget> createState() => _CartFabWidgetState();
}

class _CartFabWidgetState extends State<CartFabWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: AppAnimations.medium,
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: AppAnimations.elasticOut,
      ),
    );

    _animationController.forward();
  }

  @override
  void didUpdateWidget(CartFabWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.itemCount != widget.itemCount) {
      _animationController.reset();
      _animationController.forward();
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Container(
            margin: const EdgeInsets.all(16),
            child: FloatingActionButton.extended(
              onPressed: widget.onPressed,
              backgroundColor: const Color(0xFF00D4AA),
              foregroundColor: Colors.white,
              elevation: 4,
              label: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      widget.itemCount.toString(),
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  const Text(
                    'View Cart',
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    '\$${widget.total.toStringAsFixed(2)}',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
              icon: const Icon(Icons.shopping_cart),
            ),
          ),
        );
      },
    );
  }
}

import 'package:flutter/foundation.dart';
import '../models/restaurant.dart';
import '../models/category.dart';
import '../services/data_service.dart';

class SearchProvider with ChangeNotifier {
  final DataService _dataService = DataService();
  
  List<Restaurant> _allRestaurants = [];
  List<Restaurant> _filteredRestaurants = [];
  List<FoodCategory> _categories = [];
  
  String _searchQuery = '';
  String _selectedCategory = '';
  double _minRating = 0.0;
  int _maxDeliveryTime = 60;
  bool _isLoading = false;
  String _errorMessage = '';

  // Getters
  List<Restaurant> get filteredRestaurants => _filteredRestaurants;
  List<FoodCategory> get categories => _categories;
  String get searchQuery => _searchQuery;
  String get selectedCategory => _selectedCategory;
  double get minRating => _minRating;
  int get maxDeliveryTime => _maxDeliveryTime;
  bool get isLoading => _isLoading;
  String get errorMessage => _errorMessage;

  Future<void> loadData() async {
    _isLoading = true;
    _errorMessage = '';
    notifyListeners();

    try {
      _allRestaurants = await _dataService.getRestaurants();
      _categories = await _dataService.getCategories();
      _filteredRestaurants = _allRestaurants;
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _errorMessage = 'Failed to load data: $e';
      _isLoading = false;
      notifyListeners();
    }
  }

  void updateSearchQuery(String query) {
    _searchQuery = query;
    _applyFilters();
  }

  void updateSelectedCategory(String category) {
    _selectedCategory = _selectedCategory == category ? '' : category;
    _applyFilters();
  }

  void updateMinRating(double rating) {
    _minRating = rating;
    _applyFilters();
  }

  void updateMaxDeliveryTime(int time) {
    _maxDeliveryTime = time;
    _applyFilters();
  }

  void clearFilters() {
    _searchQuery = '';
    _selectedCategory = '';
    _minRating = 0.0;
    _maxDeliveryTime = 60;
    _applyFilters();
  }

  void _applyFilters() {
    _filteredRestaurants = _allRestaurants.where((restaurant) {
      // Search query filter
      final matchesSearch = _searchQuery.isEmpty ||
          restaurant.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          restaurant.cuisine.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          restaurant.description.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          restaurant.menu.any((item) =>
              item.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
              item.description.toLowerCase().contains(_searchQuery.toLowerCase()));

      // Category filter
      final matchesCategory = _selectedCategory.isEmpty ||
          restaurant.categories.any((category) =>
              category.toLowerCase() == _selectedCategory.toLowerCase());

      // Rating filter
      final matchesRating = restaurant.rating >= _minRating;

      // Delivery time filter
      final matchesDeliveryTime = restaurant.deliveryTime <= _maxDeliveryTime;

      return matchesSearch && matchesCategory && matchesRating && matchesDeliveryTime;
    }).toList();

    notifyListeners();
  }

  Future<List<FoodItem>> searchFoodItems(String query) async {
    if (query.isEmpty) return [];
    
    try {
      return await _dataService.searchFoodItems(query);
    } catch (e) {
      return [];
    }
  }

  List<Restaurant> getRestaurantsByCategory(String categoryName) {
    return _allRestaurants.where((restaurant) {
      return restaurant.categories.any((category) =>
          category.toLowerCase() == categoryName.toLowerCase());
    }).toList();
  }

  List<Restaurant> getFeaturedRestaurants() {
    final featured = _allRestaurants.where((restaurant) => restaurant.rating >= 4.5).toList();
    featured.sort((a, b) => b.rating.compareTo(a.rating));
    return featured.take(5).toList();
  }

  List<Restaurant> getPopularRestaurants() {
    final popular = List<Restaurant>.from(_allRestaurants);
    popular.sort((a, b) => b.reviewCount.compareTo(a.reviewCount));
    return popular.take(10).toList();
  }

  List<Restaurant> getNearbyRestaurants() {
    // For demo purposes, return all restaurants
    // In a real app, this would filter by location
    return _allRestaurants;
  }

  void sortRestaurants(SortOption sortOption) {
    switch (sortOption) {
      case SortOption.rating:
        _filteredRestaurants.sort((a, b) => b.rating.compareTo(a.rating));
        break;
      case SortOption.deliveryTime:
        _filteredRestaurants.sort((a, b) => a.deliveryTime.compareTo(b.deliveryTime));
        break;
      case SortOption.deliveryFee:
        _filteredRestaurants.sort((a, b) => a.deliveryFee.compareTo(b.deliveryFee));
        break;
      case SortOption.popularity:
        _filteredRestaurants.sort((a, b) => b.reviewCount.compareTo(a.reviewCount));
        break;
      case SortOption.alphabetical:
        _filteredRestaurants.sort((a, b) => a.name.compareTo(b.name));
        break;
    }
    notifyListeners();
  }
}

enum SortOption {
  rating,
  deliveryTime,
  deliveryFee,
  popularity,
  alphabetical,
}

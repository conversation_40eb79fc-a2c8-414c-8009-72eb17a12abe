import 'package:flutter/foundation.dart';
import '../models/cart.dart';
import '../models/restaurant.dart';

class CartProvider with ChangeNotifier {
  Cart _cart = Cart();

  Cart get cart => _cart;
  
  bool get isEmpty => _cart.isEmpty;
  bool get isNotEmpty => _cart.isNotEmpty;
  int get itemCount => _cart.itemCount;
  double get subtotal => _cart.subtotal;
  double get deliveryFee => _cart.deliveryFee;
  double get tax => _cart.tax;
  double get total => _cart.total;
  String? get restaurantId => _cart.restaurantId;
  String? get restaurantName => _cart.restaurantName;

  void addItem({
    required FoodItem foodItem,
    required String restaurantId,
    required String restaurantName,
    int quantity = 1,
    List<String> specialInstructions = const [],
  }) {
    try {
      final cartItem = CartItem(
        foodItem: foodItem,
        quantity: quantity,
        restaurantId: restaurantId,
        restaurantName: restaurantName,
        specialInstructions: specialInstructions,
      );

      _cart = _cart.addItem(cartItem);
      notifyListeners();
    } catch (e) {
      // Handle error (e.g., different restaurant)
      rethrow;
    }
  }

  void removeItem(String foodItemId) {
    _cart = _cart.removeItem(foodItemId);
    notifyListeners();
  }

  void updateItemQuantity(String foodItemId, int newQuantity) {
    _cart = _cart.updateItemQuantity(foodItemId, newQuantity);
    notifyListeners();
  }

  void clearCart() {
    _cart = _cart.clear();
    notifyListeners();
  }

  bool canAddItem(String newRestaurantId) {
    return _cart.canAddItem(newRestaurantId);
  }

  CartItem? getCartItem(String foodItemId) {
    try {
      return _cart.items.firstWhere((item) => item.foodItem.id == foodItemId);
    } catch (e) {
      return null;
    }
  }

  int getItemQuantity(String foodItemId) {
    final cartItem = getCartItem(foodItemId);
    return cartItem?.quantity ?? 0;
  }

  // Show dialog when trying to add item from different restaurant
  bool shouldShowRestaurantChangeDialog(String newRestaurantId) {
    return _cart.isNotEmpty && !_cart.canAddItem(newRestaurantId);
  }

  // Replace cart with items from new restaurant
  void replaceCartWithNewRestaurant({
    required FoodItem foodItem,
    required String restaurantId,
    required String restaurantName,
    int quantity = 1,
    List<String> specialInstructions = const [],
  }) {
    clearCart();
    addItem(
      foodItem: foodItem,
      restaurantId: restaurantId,
      restaurantName: restaurantName,
      quantity: quantity,
      specialInstructions: specialInstructions,
    );
  }
}

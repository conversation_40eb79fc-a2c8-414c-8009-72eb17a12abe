import 'restaurant.dart';

class CartItem {
  final FoodItem foodItem;
  final int quantity;
  final String restaurantId;
  final String restaurantName;
  final List<String> specialInstructions;

  CartItem({
    required this.foodItem,
    required this.quantity,
    required this.restaurantId,
    required this.restaurantName,
    this.specialInstructions = const [],
  });

  double get totalPrice => foodItem.price * quantity;

  CartItem copyWith({
    FoodItem? foodItem,
    int? quantity,
    String? restaurantId,
    String? restaurantName,
    List<String>? specialInstructions,
  }) {
    return CartItem(
      foodItem: foodItem ?? this.foodItem,
      quantity: quantity ?? this.quantity,
      restaurantId: restaurantId ?? this.restaurantId,
      restaurantName: restaurantName ?? this.restaurantName,
      specialInstructions: specialInstructions ?? this.specialInstructions,
    );
  }

  factory CartItem.fromJson(Map<String, dynamic> json) {
    return CartItem(
      foodItem: FoodItem.fromJson(json['foodItem'] as Map<String, dynamic>),
      quantity: json['quantity'] as int,
      restaurantId: json['restaurantId'] as String,
      restaurantName: json['restaurantName'] as String,
      specialInstructions: List<String>.from(json['specialInstructions'] as List? ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'foodItem': foodItem.toJson(),
      'quantity': quantity,
      'restaurantId': restaurantId,
      'restaurantName': restaurantName,
      'specialInstructions': specialInstructions,
    };
  }
}

class Cart {
  final List<CartItem> items;
  final String? restaurantId;
  final String? restaurantName;

  Cart({
    this.items = const [],
    this.restaurantId,
    this.restaurantName,
  });

  bool get isEmpty => items.isEmpty;
  bool get isNotEmpty => items.isNotEmpty;
  int get itemCount => items.fold(0, (sum, item) => sum + item.quantity);

  double get subtotal => items.fold(0.0, (sum, item) => sum + item.totalPrice);
  
  double get deliveryFee => isEmpty ? 0.0 : 2.99; // Default delivery fee
  
  double get tax => subtotal * 0.08; // 8% tax
  
  double get total => subtotal + deliveryFee + tax;

  bool canAddItem(String newRestaurantId) {
    return isEmpty || restaurantId == newRestaurantId;
  }

  Cart addItem(CartItem newItem) {
    if (!canAddItem(newItem.restaurantId)) {
      throw Exception('Cannot add items from different restaurants');
    }

    final existingItemIndex = items.indexWhere(
      (item) => item.foodItem.id == newItem.foodItem.id,
    );

    List<CartItem> updatedItems;
    if (existingItemIndex >= 0) {
      updatedItems = List.from(items);
      updatedItems[existingItemIndex] = updatedItems[existingItemIndex].copyWith(
        quantity: updatedItems[existingItemIndex].quantity + newItem.quantity,
      );
    } else {
      updatedItems = [...items, newItem];
    }

    return Cart(
      items: updatedItems,
      restaurantId: newItem.restaurantId,
      restaurantName: newItem.restaurantName,
    );
  }

  Cart removeItem(String foodItemId) {
    final updatedItems = items.where((item) => item.foodItem.id != foodItemId).toList();
    
    return Cart(
      items: updatedItems,
      restaurantId: updatedItems.isEmpty ? null : restaurantId,
      restaurantName: updatedItems.isEmpty ? null : restaurantName,
    );
  }

  Cart updateItemQuantity(String foodItemId, int newQuantity) {
    if (newQuantity <= 0) {
      return removeItem(foodItemId);
    }

    final updatedItems = items.map((item) {
      if (item.foodItem.id == foodItemId) {
        return item.copyWith(quantity: newQuantity);
      }
      return item;
    }).toList();

    return Cart(
      items: updatedItems,
      restaurantId: restaurantId,
      restaurantName: restaurantName,
    );
  }

  Cart clear() {
    return Cart();
  }

  factory Cart.fromJson(Map<String, dynamic> json) {
    return Cart(
      items: (json['items'] as List)
          .map((item) => CartItem.fromJson(item as Map<String, dynamic>))
          .toList(),
      restaurantId: json['restaurantId'] as String?,
      restaurantName: json['restaurantName'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'items': items.map((item) => item.toJson()).toList(),
      'restaurantId': restaurantId,
      'restaurantName': restaurantName,
    };
  }
}

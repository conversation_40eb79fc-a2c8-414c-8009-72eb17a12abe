import 'package:flutter/material.dart';
import 'animations.dart';

class NavigationHelper {
  static Future<T?> slideToPage<T>(
    BuildContext context,
    Widget page, {
    SlideDirection direction = SlideDirection.right,
  }) {
    return Navigator.push<T>(
      context,
      AppAnimations.slideTransition<T>(
        page: page,
        settings: RouteSettings(name: page.runtimeType.toString()),
        direction: direction,
      ),
    );
  }

  static Future<T?> fadeToPage<T>(
    BuildContext context,
    Widget page,
  ) {
    return Navigator.push<T>(
      context,
      AppAnimations.fadeTransition<T>(
        page: page,
        settings: RouteSettings(name: page.runtimeType.toString()),
      ),
    );
  }

  static Future<T?> scaleToPage<T>(
    BuildContext context,
    Widget page,
  ) {
    return Navigator.push<T>(
      context,
      AppAnimations.scaleTransition<T>(
        page: page,
        settings: RouteSettings(name: page.runtimeType.toString()),
      ),
    );
  }

  static void popWithAnimation(BuildContext context) {
    Navigator.pop(context);
  }
}

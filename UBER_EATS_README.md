# Uber Eats Clone - Flutter App

A fully functional Uber Eats clone built with Flutter, featuring local JSON data storage, comprehensive UI components, and state management.

## 🚀 Features

### Core Functionality
- **Restaurant Browsing**: Browse restaurants with detailed information including ratings, delivery time, and fees
- **Category Filtering**: Filter restaurants by cuisine categories (Pizza, Burgers, Sushi, Italian, Indian, etc.)
- **Search & Filters**: Advanced search with filters for rating, delivery time, and categories
- **Restaurant Details**: Detailed restaurant pages with menu items organized by categories
- **Shopping Cart**: Add/remove items, quantity management, and cart persistence
- **Checkout Process**: Complete checkout flow with address, payment method selection, and order placement

### UI/UX Features
- **Modern Design**: Clean, modern UI inspired by Uber Eats
- **Responsive Layout**: Works on different screen sizes
- **Loading States**: Proper loading indicators and error handling
- **Animations**: Smooth transitions and interactions
- **Image Caching**: Cached network images for better performance
- **Rating System**: Star ratings for restaurants and food items

### Technical Features
- **State Management**: Provider pattern for cart and search state
- **Local Data**: JSON files for mock restaurant and category data
- **Network Simulation**: Simulated API calls with realistic delays
- **Type Safety**: Comprehensive Dart models with JSON serialization
- **Error Handling**: Robust error handling throughout the app

## 📱 Screens

1. **Home Screen**
   - Restaurant list with search and category filters
   - Featured restaurants section
   - Cart floating action button

2. **Restaurant Detail Screen**
   - Restaurant information and ratings
   - Menu items organized by categories
   - Add to cart functionality with quantity controls

3. **Search Screen**
   - Advanced search with multiple filters
   - Sort options (rating, delivery time, popularity)
   - Real-time filtering

4. **Cart Screen**
   - Cart items with quantity controls
   - Order summary with taxes and fees
   - Clear cart functionality

5. **Checkout Screen**
   - Delivery address input
   - Payment method selection
   - Order placement simulation

## 🏗️ Architecture

### Project Structure
```
lib/
├── models/           # Data models (Restaurant, Cart, Category, etc.)
├── providers/        # State management (CartProvider, SearchProvider)
├── screens/          # UI screens
├── services/         # Data services (DataService)
├── widgets/          # Reusable UI components
└── main_uber_eats.dart  # App entry point

assets/
├── data/            # JSON mock data files
└── images/          # App images (placeholder)
```

### Key Models
- **Restaurant**: Complete restaurant information with menu
- **FoodItem**: Individual menu items with dietary information
- **Cart & CartItem**: Shopping cart management
- **Order**: Order processing and status tracking
- **FoodCategory**: Restaurant and food categorization

### State Management
- **CartProvider**: Manages shopping cart state across the app
- **SearchProvider**: Handles search, filtering, and sorting logic

## 🛠️ Setup & Installation

### Prerequisites
- Flutter SDK (latest stable version)
- Dart SDK
- iOS Simulator or Android Emulator

### Dependencies
```yaml
dependencies:
  flutter:
    sdk: flutter
  provider: ^6.1.2
  cached_network_image: ^3.3.1
  flutter_rating_bar: ^4.0.1
  cupertino_icons: ^1.0.8
```

### Installation Steps
1. Clone the repository
2. Navigate to the project directory
3. Run `flutter pub get` to install dependencies
4. Run `flutter run lib/main_uber_eats.dart` to start the app

## 📊 Mock Data

The app uses local JSON files for mock data:

### Restaurants Data (`assets/data/restaurants.json`)
- 4 sample restaurants with complete information
- Each restaurant includes menu items with detailed information
- Covers different cuisines: Italian, Japanese, American, Indian

### Categories Data (`assets/data/categories.json`)
- 10 food categories with icons and descriptions
- Popular categories marked for featured display

## 🎨 Design System

### Colors
- Primary: `#00D4AA` (Uber Eats green)
- Background: White
- Text: Black/Grey variations
- Error: Red
- Success: Green

### Typography
- Headers: Bold, 18-24px
- Body: Regular, 14-16px
- Captions: 12px

### Components
- Cards with rounded corners (12px radius)
- Elevated buttons with primary color
- Chips for categories and filters
- Rating stars with amber color

## 🔄 State Flow

1. **App Launch**: Load restaurants and categories from JSON
2. **Browse**: Filter and search restaurants
3. **Restaurant Selection**: View menu and add items to cart
4. **Cart Management**: Modify quantities, view totals
5. **Checkout**: Enter details and place order
6. **Order Completion**: Clear cart and return to home

## 🚀 Future Enhancements

- User authentication and profiles
- Real-time order tracking
- Push notifications
- Payment integration
- Location services
- Restaurant reviews and ratings
- Favorites and order history
- Multi-language support

## 🧪 Testing

The app includes comprehensive error handling and loading states. To test:

1. **Network Simulation**: Delays are built into data loading
2. **Error States**: Try clearing data or simulating network errors
3. **Cart Logic**: Test adding items from different restaurants
4. **Search & Filters**: Test various combinations of filters
5. **Checkout Flow**: Complete the full order process

## 📝 Notes

- This is a demo app with simulated data and functionality
- Images are loaded from Unsplash for demonstration
- Payment processing is simulated (no real transactions)
- Location services are mocked with static data

## 🤝 Contributing

This is a learning project demonstrating Flutter development best practices:
- Clean architecture with separation of concerns
- Proper state management with Provider
- Comprehensive error handling
- Modern UI/UX design patterns
- Type-safe Dart code with proper models

Feel free to use this as a reference for building similar apps or extending the functionality!

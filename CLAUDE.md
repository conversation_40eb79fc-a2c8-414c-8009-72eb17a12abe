# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Flutter project that contains multiple applications:
1. **Dice Rolling App** (`_main.dart`) - Simple Flutter learning project 
2. **JSON Posts App** (`main.dart`) - HTTP request demo with JSONPlaceholder API
3. **Uber Eats Clone** (`main_uber_eats.dart`) - Full-featured food delivery app simulation

## Common Development Commands

### Essential Flutter Commands
- `flutter pub get` - Install dependencies
- `flutter run` - Run the default app (JSON Posts)
- `flutter run lib/main_uber_eats.dart` - Run the Uber Eats clone
- `flutter run lib/_main.dart` - Run the simple dice app
- `flutter test` - Run widget tests
- `flutter analyze` - Static analysis with lints
- `flutter build apk` - Build Android APK
- `flutter build ios` - Build iOS app

### Testing and Quality
- `flutter test test/widget_test.dart` - Run specific widget tests
- Tests import from `package:_02_role_the_die/_main.dart`

## Architecture Overview

### Uber Eats Clone (Primary App)
**State Management:** Provider pattern with `CartProvider` and `SearchProvider`

**Data Layer:**
- `DataService` - Singleton service for loading JSON data with network simulation
- Local JSON mock data in `assets/data/` (restaurants.json, categories.json)
- Models: `Restaurant`, `FoodItem`, `Cart`, `CartItem`, `FoodCategory`

**UI Structure:**
- `HomeScreen` - Main restaurant listing with category filters
- `RestaurantDetailScreen` - Menu display with cart integration
- `CartScreen` - Shopping cart management
- `CheckoutScreen` - Order completion flow
- `SearchScreen` - Advanced search with filters

**Key Features:**
- Cart management with single-restaurant restriction
- Search and filtering with multiple criteria
- Cached network images for performance
- Error handling with retry mechanisms
- Loading states and network delay simulation

### Project Structure
```
lib/
├── models/          # Data models with JSON serialization
├── providers/       # Provider-based state management
├── screens/         # UI screens and navigation
├── services/        # Data services and API simulation
├── widgets/         # Reusable UI components
├── utils/          # Utilities and helpers
├── main.dart       # JSON Posts app entry point
├── main_uber_eats.dart  # Uber Eats clone entry point
└── _main.dart      # Simple dice app entry point
```

## Key Dependencies
- `provider: ^6.1.2` - State management
- `cached_network_image: ^3.3.1` - Image caching
- `flutter_rating_bar: ^4.0.1` - Rating UI components
- `http: ^1.2.1` - HTTP requests

## Development Notes

### Code Style
- Strict analysis options enabled (`strict-casts`, `strict-inference`)
- Trailing commas required for better formatting
- Uses `flutter_lints` package for code quality

### Testing
- Widget tests reference `_main.dart` for simple app testing
- Test structure follows Flutter conventions

### Data Flow
- JSON data loaded from assets with simulated network delays
- Provider pattern manages cart state across screens
- Models include comprehensive JSON serialization
- Error handling with user-friendly messages

## Asset Structure
- `assets/data/` - JSON mock data files
- `assets/images/` - App images (placeholder structure)
- Images sourced from Unsplash for demonstration purposes